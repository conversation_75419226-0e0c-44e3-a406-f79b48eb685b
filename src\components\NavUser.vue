<script setup lang="ts">
/**
 * NavUser component displays user information in the sidebar navigation.
 *
 * Features:
 * - Shows user avatar (prefers player avatar over user avatar)
 * - Displays user name (prefers player first/last name over email)
 * - Shows player location (city, country)
 * - Shows player equipment category
 * - Provides dropdown menu with account actions
 *
 * The component integrates with both auth store (for basic user data)
 * and user store (for detailed player profile information).
 */
import { computed } from 'vue' // Added
import { useRouter } from 'vue-router' // Added
import { useAuthStore, type User as AuthUser } from '@/stores/auth' // Added
import { useUserStore } from '@/stores/user' // Added
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/avatar'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import {
  // BadgeCheck, // Not used
  Bell,
  ChevronsUpDown,
  // CreditCard, // Not used
  LogOut,
  // Sparkles, // Not used
  User,
  Settings,
  MapPin,
  Target,
} from 'lucide-vue-next'

// const props = defineProps<{ // Props are no longer needed as we use the store
//   user: {
//     name: string
//     email: string
//     avatar: string
//   }
// }>()

const authStore = useAuthStore()
const userStore = useUserStore()
const router = useRouter()
const { isMobile } = useSidebar()

const currentUser = computed(() => authStore.user as AuthUser | null) // Use user from store

const userInitials = computed(() => {
  // Try to get initials from player name first
  const player = userStore.primaryPlayer
  if (player?.firstname || player?.lastname) {
    const firstInitial = player.firstname?.[0]?.toUpperCase() || ''
    const lastInitial = player.lastname?.[0]?.toUpperCase() || ''
    if (firstInitial && lastInitial) {
      return firstInitial + lastInitial
    }
    if (firstInitial || lastInitial) {
      return (firstInitial || lastInitial) + (player.firstname || player.lastname)?.substring(1, 2)?.toUpperCase() || ''
    }
  }

  // Fallback to auth user data
  if (currentUser.value) {
    if (typeof currentUser.value.name === 'string' && currentUser.value.name) {
      const parts = currentUser.value.name.split(' ')
      if (parts.length > 1 && parts[0] && parts[1]) {
        return parts[0][0].toUpperCase() + parts[1][0].toUpperCase()
      }
      return currentUser.value.name.substring(0, 2).toUpperCase()
    }
    if (currentUser.value.email) {
      return currentUser.value.email.substring(0, 2).toUpperCase()
    }
  }
  return 'AP' // Default fallback
})

const userDisplayName = computed(() => {
  // Prefer player name from user store, fallback to auth user data
  if (userStore.fullName && userStore.fullName !== userStore.userProfile?.email) {
    return userStore.fullName
  }
  return currentUser.value?.name || currentUser.value?.email || 'User'
})

const userDisplayEmail = computed(() => {
  return currentUser.value?.email || ''
})

const userDisplayLocation = computed(() => {
  const player = userStore.primaryPlayer
  if (!player) return null

  const parts = [player.city].filter(Boolean)
  return parts.length > 0 ? parts.join(', ') : null
})

const userCountryCode = computed(() => {
  const player = userStore.primaryPlayer
  if (!player?.country) return null

  // Convert country name to country code (you may want to expand this mapping)
  const countryMap: Record<string, string> = {
    'Poland': 'pl',
    'Germany': 'de',
    'France': 'fr',
    'United Kingdom': 'gb',
    'United States': 'us',
    'Canada': 'ca',
    'Italy': 'it',
    'Spain': 'es',
    'Netherlands': 'nl',
    'Sweden': 'se',
    'Norway': 'no',
    'Denmark': 'dk',
    'Finland': 'fi',
    'Czech Republic': 'cz',
    'Slovakia': 'sk',
    'Austria': 'at',
    'Switzerland': 'ch',
    'Belgium': 'be',
    'Hungary': 'hu',
    'Romania': 'ro',
    'Bulgaria': 'bg',
    'Croatia': 'hr',
    'Slovenia': 'si',
    'Lithuania': 'lt',
    'Latvia': 'lv',
    'Estonia': 'ee'
  }

  return countryMap[player.country] || player.country.toLowerCase().substring(0, 2)
})

const userCountryFlag = computed(() => {
  const countryCode = userCountryCode.value
  return countryCode ? `/flags/${countryCode}.svg` : null
})

const userDisplayEquipment = computed(() => {
  return userStore.playerEquipmentCategory || null
})

const userAvatarSrc = computed(() => {
  // Prefer player avatar from user store, fallback to auth user avatar
  const playerAvatar = userStore.primaryPlayer?.avatar
  if (playerAvatar && typeof playerAvatar === 'string') {
    return playerAvatar
  }

  // Fallback to user avatar from auth store
  return typeof currentUser.value?.avatar === 'string' ? currentUser.value.avatar : ''
})

async function handleLogout() {
  await authStore.logout()
  router.push({ name: 'login' }) // Explicitly redirect to login
}
</script>

<template>
  <SidebarMenu v-if="authStore.isAuthenticated && currentUser">
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <Avatar class="h-8 w-8 rounded-lg">
              <AvatarImage :src="userAvatarSrc" :alt="userDisplayName" />
              <AvatarFallback class="rounded-lg">
                {{ userInitials }}
              </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-medium">{{ userDisplayName }}</span>
              <div class="space-y-0.5">
                <span v-if="userDisplayEmail" class="truncate text-xs block">
                  {{ userDisplayEmail }}
                </span>
                <span v-if="userDisplayLocation" class="truncate text-xs text-muted-foreground flex items-center gap-1">
                  <MapPin class="h-3 w-3" />
                  {{ userDisplayLocation }}
                </span>

              </div>
            </div>
            <ChevronsUpDown class="ml-auto size-4" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          :side="isMobile ? 'bottom' : 'bottom'"
          align="end"
          :side-offset="4"
        >
          <DropdownMenuLabel class="p-0 font-normal">
            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
              <Avatar class="h-8 w-8 rounded-lg">
                <AvatarImage :src="userAvatarSrc" :alt="userDisplayName" />
                <AvatarFallback class="rounded-lg">
                  {{ userInitials }}
                </AvatarFallback>
              </Avatar>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">{{ userDisplayName }}</span>
                <div class="space-y-0.5">
                  <span v-if="userDisplayEmail" class="truncate text-xs block">
                    {{ userDisplayEmail }}
                  </span>
                  <span v-if="userDisplayLocation" class="truncate text-xs text-muted-foreground flex items-center gap-1">
                    <MapPin class="h-3 w-3" />
                    {{ userDisplayLocation }}
                  </span>
                  <span v-if="userDisplayEquipment" class="truncate text-xs text-muted-foreground flex items-center gap-1">
                    <Target class="h-3 w-3" />
                    {{ userDisplayEquipment }}
                  </span>
                </div>
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem @click="router.push('/account')"> <!-- TODO: Create /account route -->
              <User class="mr-2 h-4 w-4" /> <!-- Added class for spacing -->
              Account
            </DropdownMenuItem>
            <DropdownMenuItem @click="router.push('/settings')"> <!-- TODO: Create /settings route -->
              <Settings class="mr-2 h-4 w-4" /> <!-- Added class for spacing -->
              Settings
            </DropdownMenuItem>
            <DropdownMenuItem @click="router.push('/notifications')"> <!-- TODO: Create /notifications route -->
              <Bell class="mr-2 h-4 w-4" /> <!-- Added class for spacing -->
              Notifications
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="handleLogout">
            <LogOut class="mr-2 h-4 w-4" /> <!-- Added class for spacing -->
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
  <SidebarMenu v-else>
    <SidebarMenuItem>
       <SidebarMenuButton
        size="lg"
        @click="router.push({ name: 'login' })"
      >
        <LogOut class="mr-2 h-4 w-4 rotate-180" /> <!-- Icon indicating login -->
        Sign In
      </SidebarMenuButton>
    </SidebarMenuItem>
  </SidebarMenu>
</template>
