<script setup lang="ts">
/**
 * GearSwitcher component displays and manages user's archery equipment.
 *
 * Features:
 * - Shows current selected equipment
 * - Displays equipment list in dropdown
 * - Allows switching between equipment
 * - Provides option to add new equipment
 * - Integrates with equipment store for state management
 */
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { ChevronsUpDown, Plus, Target, Zap } from 'lucide-vue-next'

import { computed } from 'vue'
import { useEquipmentStore } from '@/stores/equipment'
import { useAuthStore } from '@/stores/auth'

const equipmentStore = useEquipmentStore()
const authStore = useAuthStore()
const { isMobile } = useSidebar()

// Computed properties for display
const currentEquipment = computed(() => equipmentStore.currentEquipment)
const equipmentList = computed(() => equipmentStore.activeEquipment)
const hasEquipment = computed(() => equipmentStore.hasEquipment)

// Equipment display helpers
const getEquipmentIcon = (equipment: any) => {
  // Return appropriate icon based on equipment type/category
  if (equipment?.category?.toLowerCase().includes('recurve')) return Target
  if (equipment?.category?.toLowerCase().includes('compound')) return Zap
  return Target // Default icon
}

const getEquipmentDisplayName = (equipment: any) => {
  return equipment?.name || 'Unknown Equipment'
}

const getEquipmentCategory = (equipment: any) => {
  return equipment?.category || equipment?.type || 'Equipment'
}

// Actions
function selectEquipment(equipment: any) {
  equipmentStore.selectEquipment(equipment)
}

function handleAddEquipment() {
  // TODO: Open equipment creation modal/form
  console.log('Add new equipment')
}
</script>

<template>
  <SidebarMenu v-if="authStore.isAuthenticated">
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground bg-sidebar-primary text-sidebar-primary-foreground"
            :disabled="equipmentStore.isLoading"
          >
            <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <component
                v-if="currentEquipment"
                :is="getEquipmentIcon(currentEquipment)"
                class="size-4"
              />
              <Target v-else class="size-4" />
            </div>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-medium">
                {{ currentEquipment ? getEquipmentDisplayName(currentEquipment) : 'No Equipment' }}
              </span>
              <span class="truncate text-xs">
                {{ currentEquipment ? getEquipmentCategory(currentEquipment) : 'Select equipment' }}
              </span>
            </div>
            <ChevronsUpDown class="ml-auto" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          align="start"
          :side="isMobile ? 'bottom' : 'right'"
          :side-offset="4"
        >
          <DropdownMenuLabel class="text-xs text-muted-foreground">
            My equipment
          </DropdownMenuLabel>

          <!-- Loading state -->
          <DropdownMenuItem v-if="equipmentStore.isLoading" disabled class="gap-2 p-2">
            <div class="flex size-6 items-center justify-center rounded-sm border">
              <Target class="size-3.5 shrink-0 animate-pulse" />
            </div>
            Loading equipment...
          </DropdownMenuItem>

          <!-- Equipment list -->
          <DropdownMenuItem
            v-for="(equipment, index) in equipmentList"
            :key="equipment.id"
            class="gap-2 p-2"
            :class="{ 'bg-accent': currentEquipment?.id === equipment.id }"
            @click="selectEquipment(equipment)"
          >
            <div class="flex size-6 items-center justify-center rounded-sm border">
              <component :is="getEquipmentIcon(equipment)" class="size-3.5 shrink-0" />
            </div>
            <div class="flex-1">
              <div class="font-medium">{{ getEquipmentDisplayName(equipment) }}</div>
              <div class="text-xs text-muted-foreground">{{ getEquipmentCategory(equipment) }}</div>
            </div>
            <DropdownMenuShortcut v-if="equipment.isDefault">★</DropdownMenuShortcut>
            <DropdownMenuShortcut v-else>{{ index + 1 }}</DropdownMenuShortcut>
          </DropdownMenuItem>

          <!-- No equipment state -->
          <DropdownMenuItem v-if="!equipmentStore.isLoading && !hasEquipment" disabled class="gap-2 p-2">
            <div class="flex size-6 items-center justify-center rounded-sm border">
              <Target class="size-3.5 shrink-0 text-muted-foreground" />
            </div>
            <div class="text-muted-foreground">No equipment found</div>
          </DropdownMenuItem>

          <DropdownMenuSeparator />
          <DropdownMenuItem class="gap-2 p-2" @click="handleAddEquipment">
            <div class="flex size-6 items-center justify-center rounded-md border bg-transparent">
              <Plus class="size-4" />
            </div>
            <div class="font-medium text-muted-foreground">
              Add equipment
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
</template>
