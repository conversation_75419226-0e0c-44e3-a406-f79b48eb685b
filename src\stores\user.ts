import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue'

import type { User, UserPatch, UserProfile } from '../api/feathers-client'
import { api } from '../api/feathers-client'
import { useAuthStore } from './auth'

/**
 * User store for managing user profile data from the user-me service.
 *
 * This store automatically fetches the user profile when the user is authenticated
 * and clears it when the user logs out.
 *
 * Usage:
 * ```ts
 * const userStore = useUserStore()
 *
 * // Access user profile
 * console.log(userStore.userProfile)
 *
 * // Update user profile
 * await userStore.updateUserProfile({ avatar: 'new-avatar-url' })
 *
 * // Manually fetch profile
 * await userStore.fetchUserProfile()
 * ```
 */

export const useUserStore = defineStore('user', () => {
  // Use the typed service from the api client
  const userMeService = api.userMe

  const userProfile = ref<UserProfile | null>(null)
  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  // Computed properties
  const hasProfile = computed(() => !!userProfile.value)
  const fullName = computed(() => {
    if (!userProfile.value) return ''
    // Try to get name from related player data if available
    // For now, just return email as fallback
    return userProfile.value.email
  })

  // Actions
  async function fetchUserProfile() {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      error.value = new Error('User not authenticated')
      return
    }

    isLoading.value = true
    error.value = null

    try {
      const profile = await userMeService.find()
      userProfile.value = profile
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to fetch user profile')
      }
      userProfile.value = null
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function updateUserProfile(data: Partial<User>) {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      error.value = new Error('User not authenticated')
      return
    }

    isLoading.value = true
    error.value = null

    try {
      const updatedProfile = await userMeService.patch('me', data)
      // Merge the updated data with the current profile
      if (userProfile.value) {
        userProfile.value = { ...userProfile.value, ...updatedProfile }
      }
      return updatedProfile
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to update user profile')
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  function clearUserProfile() {
    userProfile.value = null
    error.value = null
  }

  // Watch for authentication changes and auto-fetch profile
  const authStore = useAuthStore()

  // Auto-fetch profile when user becomes authenticated
  watch(
    () => authStore.isAuthenticated,
    (isAuthenticated) => {
      if (isAuthenticated && !userProfile.value) {
        fetchUserProfile()
      } else if (!isAuthenticated) {
        clearUserProfile()
      }
    },
    { immediate: true }
  )

  return {
    userProfile,
    isLoading,
    error,
    hasProfile,
    fullName,
    fetchUserProfile,
    updateUserProfile,
    clearUserProfile
  }
})
